const { defineConfig } = require("@vue/cli-service");
const AutoImport = require("unplugin-auto-import/webpack");
const Components = require("unplugin-vue-components/webpack");
const { NaiveUiResolver } = require("unplugin-vue-components/resolvers");
const { codeInspectorPlugin } = require("code-inspector-plugin");

// 获取当前环境
const currentEnv = process.env.NODE_ENV || 'development';
const vueAppEnv = process.env.VUE_APP_ENV || 'development';

// 环境配置
const envConfig = {
  development: {
    sourceMap: true,
    productionGzip: false,
    bundleAnalyzer: false,
  },
  test: {
    sourceMap: true,
    productionGzip: true,
    bundleAnalyzer: false,
  },
  staging: {
    sourceMap: false,
    productionGzip: true,
    bundleAnalyzer: false,
  },
  production: {
    sourceMap: false,
    productionGzip: true,
    bundleAnalyzer: false,
  }
};

const config = envConfig[vueAppEnv] || envConfig.development;

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,
  // 根据环境配置source map
  productionSourceMap: config.sourceMap,
  // 输出目录根据环境命名
  outputDir: `dist-${vueAppEnv}`,
  devServer: {
    port: 8088,
    client: {
      overlay: {
        warnings: false, // 不显示警告
        runtimeErrors: (error) => {
          if (error.message.includes("ResizeObserver loop")) {
            return false;
          }
          return true;
        },
      },
    },
    proxy: {
      "/api": {
        target: "http://localhost:8082",
        changeOrigin: true,
        pathRewrite: {
          "^/api": "/api",
        },
      },
    },
  },
  configureWebpack: {
    plugins: [
      AutoImport({
        imports: [
          "vue",
          {
            "naive-ui": [
              "useDialog",
              "useMessage",
              "useNotification",
              "useLoadingBar",
            ],
          },
        ],
      }),
      Components({
        resolvers: [NaiveUiResolver()],
      }),
      // code-inspector-plugin 配置 - 仅在开发环境启用
      ...(vueAppEnv === 'development' ? [codeInspectorPlugin({
        bundler: "webpack",
        // 设置 IDE 为 Cursor，可根据需要修改
        editor: "/Applications/Cursor.app/Contents/MacOS/Cursor",
        // 如果需要切换到 Windsurf，使用下面的路径
        // editor: "/Users/<USER>/.codeium/windsurf/bin/windsurf",
        // 开发环境下启用
        dev: true,
        // 生产环境下禁用
        build: false,
      })] : []),
    ],
    stats: {
      warningsFilter: [
        /Deprecation/,
        /sass-loader/,
        /The legacy JS API is deprecated/,
      ],
    },
    // 环境变量定义
    define: {
      __VUE_APP_ENV__: JSON.stringify(vueAppEnv),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    },
  },
  css: {
    loaderOptions: {
      sass: {
        api: "modern", // 使用现代 Sass API
        sassOptions: {
          quietDeps: true,
        },
      },
    },
  },
});
