import axios from "axios";
import { useMessage } from "naive-ui";

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_API_BASE_URL || "/api", // 默认API地址
  timeout: 10000, // 请求超时时间
  headers: {
    "Content-Type": "application/json;charset=utf-8",
  },
});

// 错误处理函数
const errorHandler = (error) => {
  const message = useMessage();

  let msg = "请求错误";
  if (error.response) {
    const { status, data } = error.response;

    // 根据状态码处理不同的错误
    switch (status) {
      case 400:
        msg = data.msg || "请求参数错误";
        break;
      case 401:
        msg = "未授权，请重新登录";
        // 清除token，跳转登录页
        localStorage.removeItem("token");
        window.location.href = "/login";
        break;
      case 403:
        msg = "拒绝访问";
        break;
      case 404:
        msg = `请求地址出错: ${error.response.config.url}`;
        break;
      case 500:
        msg = data.msg || "服务器内部错误";
        break;
      default:
        msg = data.msg || `未知错误 ${status}`;
    }
  } else if (error.request) {
    msg = "服务器无响应";
  } else {
    msg = error.message;
  }

  message.error(msg);
  return Promise.reject(error);
};

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 在请求头中添加token
    const token = localStorage.getItem("token");
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use((response) => {
  const res = response.data;

  // 如果状态码不是200，认为请求失败
  if (res.code !== 200) {
    const message = useMessage();
    message.error(res.msg || "请求失败");

    // 401: 未登录或token过期
    if (res.code === 401) {
      // 清除token，跳转登录页
      localStorage.removeItem("token");
      window.location.href = "/login";
    }

    return Promise.reject(new Error(res.msg || "请求失败"));
  }

  return res;
}, errorHandler);

export default service;
