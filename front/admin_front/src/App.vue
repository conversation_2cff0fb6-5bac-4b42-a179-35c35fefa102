<template>
  <n-config-provider :theme="theme" :locale="zhCN" :date-locale="dateZhCN">
    <n-message-provider>
      <n-loading-bar-provider>
        <n-dialog-provider>
          <n-notification-provider>
            <n-modal-provider>
              <router-view></router-view>
            </n-modal-provider>
          </n-notification-provider>
        </n-dialog-provider>
      </n-loading-bar-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup>
  import { computed } from "vue";
  import { darkTheme, useOsTheme, zhCN, dateZhCN } from "naive-ui";

  // 主题设置
  const osThemeRef = useOsTheme();
  const theme = computed(() => {
    // 这里可以根据需要返回darkTheme或null（浅色主题）
    // return osThemeRef.value === 'dark' ? darkTheme : null;
    return null; // 始终使用浅色主题
  });
</script>

<style>
  html,
  body,
  #app {
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
  }
</style>
