import axios from "axios";
import { useMessage } from "naive-ui";
import { getToken } from "@/utils/token";

const message = useMessage();

// 创建axios实例
const service = axios.create({
  // baseURL: "http://118.24.74.226:8082/api",
  baseURL: "/api",
  timeout: 15000,
  withCredentials: true, // 支持跨域cookie
});

// 创建文件上传专用的axios实例
const uploadService = axios.create({
  // baseURL: "http://118.24.74.226:8082/api",
  baseURL: "/api",
  timeout: 300000, // 5分钟超时，适合大文件上传
  withCredentials: true, // 支持跨域cookie
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 使用工具函数获取token
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error("请求错误：", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;

    // 如果code不为200，表示请求异常
    if (res.code !== 200) {
      message.error(res.message || "请求失败");

      // 401: 未登录或token过期
      if (res.code === 401) {
        localStorage.removeItem("token");
        window.location.href = "/#/login";
      }

      return Promise.reject(new Error(res.message || "请求失败"));
    } else {
      return res;
    }
  },
  (error) => {
    console.error("响应错误：", error);
    message.error(error.message || "网络请求异常");
    return Promise.reject(error);
  }
);

// 文件上传请求拦截器
uploadService.interceptors.request.use(
  (config) => {
    // 使用工具函数获取token
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error("上传请求错误：", error);
    return Promise.reject(error);
  }
);

// 文件上传响应拦截器
uploadService.interceptors.response.use(
  (response) => {
    const res = response.data;

    // 如果code不为200，表示请求异常
    if (res.code !== 200) {
      message.error(res.msg || res.message || "上传失败");

      // 401: 未登录或token过期
      if (res.code === 401) {
        localStorage.removeItem("token");
        window.location.href = "/#/login";
      }

      return Promise.reject(new Error(res.msg || res.message || "上传失败"));
    } else {
      return res;
    }
  },
  (error) => {
    console.error("上传响应错误：", error);
    message.error(error.message || "上传失败");
    return Promise.reject(error);
  }
);

export default service;
export { uploadService };
