spring:
  application:
    name: dianfeng-class
  # 激活的配置文件，默认为dev
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************
    username: dianfeng_class
    password: tiMzeNWW3QKydYsr
    hikari:
      # 连接池配置
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000
  # Redis配置
  data:
    redis:
      host: *************
      port: 16379
      password: redis_Rekn6i
      database: 0
      # 连接超时时间（毫秒）
      timeout: 5000
      # 连接超时时间（毫秒）
      connect-timeout: 3000
      client-name: dianfeng-redis-client
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 20
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: 3000ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 2
          # 空闲连接检测间隔时间（毫秒）
          time-between-eviction-runs: 30000ms
        # 关闭超时时间
        shutdown-timeout: 200ms
        # 集群配置（如果使用集群）
        cluster:
          refresh:
            # 启用自适应集群拓扑刷新
            adaptive: true
            # 集群拓扑刷新周期
            period: 30s
  devtools:
    restart:
      enabled: false
# 服务器配置
server:
  port: 8082
  servlet:
    context-path: /api

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: pox.com.dianfeng.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDel
      logic-delete-value: 1
      logic-not-delete-value: 0
      table-underline: true

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: -1
  # token临时有效期 (指定时间内无操作就过期) 单位: 秒
  active-timeout: 18000000000
  # 是否允许同一账号并发登录 (为true时允许一个账号多地同时登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false

# Swagger配置
springfox:
  documentation:
    swagger-ui:
      enabled: true
    enabled: true
    swagger:
      v2:
        enabled: true

# 日志配置
logging:
  level:
    root: info
    pox.com.dianfeng: debug
  file:
    name: logs/dianfeng-class.log

# OSS 对象存储配置 - 阿里云OSS接入点
oss:
  base-url: https://dianfeng-class.oss-cn-chengdu.aliyuncs.com/
  # 阿里云OSS接入点外网endpoint地址
  endpoint: https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com
  # 预览用的url，和上传文件的endpoint分开，如果不填则默认为endpoint
  preview-url: https://demo-1282081849547244.oss-cn-chengdu.oss-accesspoint.aliyuncs.com
  # 所在地区 - 西南1（成都）
  region: cn-chengdu
  # 阿里云访问密钥ID
  access-key: LTAI5tP9cnnNBvQzusQRuMuh
  # 阿里云访问密钥Secret
  secret-key: ******************************
  # 默认存储桶名称
  bucket-name: dianfeng-class
  # 禁用存储桶检查（避免权限问题）
  check-bucket: false
  # 使用路径样式访问（接入点推荐）
  path-style-access: true
  # 设置会过期的子文件桶
  expiring-buckets:
    temp-uploads: 7    # 临时上传文件，7天后删除
    cache-files: 30    # 缓存文件，30天后删除

# 阿里云OSS PostObject签名配置 - 用于前端直传OSS
aliyun:
  oss:
    # PostObject签名配置
    post-object:
      # 签名有效期（秒），建议30分钟
      expire-seconds: 1800
      # 允许的最大文件大小（字节）
      max-file-size: 524288000  # 500MB
      # 允许的文件类型
      allowed-content-types:
        - "image/jpeg"
        - "image/jpg"
        - "image/png"
        - "image/gif"
        - "image/webp"
        - "image/bmp"
        - "video/mp4"
        - "video/avi"
        - "video/mov"
        - "video/wmv"
        - "video/flv"
        - "video/mkv"
        - "video/webm"
        - "audio/mp3"
        - "audio/wav"
        - "audio/aac"
        - "audio/flac"
        - "audio/ogg"
        - "audio/m4a"
        - "application/pdf"
        - "application/msword"
        - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
